# 即梦AI图片注入扩展 - 安装使用指南

## 🚀 快速开始

### 第一步：安装扩展

1. **打开Chrome浏览器**
2. **进入扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者点击右上角三点菜单 → 更多工具 → 扩展程序

3. **开启开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开启（变为蓝色状态）

4. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择包含所有扩展文件的文件夹
   - 点击"选择文件夹"

5. **确认安装**
   - 扩展列表中出现"即梦AI图片注入扩展"
   - 浏览器工具栏出现扩展图标

### 第二步：准备图片

确保您的图片URL可以正常访问：

**默认图片地址：**
- 首帧：`http://www.leduoae.com/ceshitupian/1.png`
- 尾帧：`http://www.leduoae.com/ceshitupian/2.png`

**测试图片是否可用：**
1. 在浏览器新标签页中打开图片URL
2. 确认图片能正常显示
3. 如果无法显示，请更换其他图片URL

### 第三步：使用扩展

1. **打开即梦AI页面**
   ```
   https://jimeng.jianying.com/ai-tool/generate?type=video
   ```

2. **点击扩展图标**
   - 在浏览器工具栏找到扩展图标
   - 点击打开控制面板

3. **配置参数**
   - **首帧图片URL**：输入或确认首帧图片地址
   - **尾帧图片URL**：输入或确认尾帧图片地址
   - **提示词**：输入描述视频效果的文字
   - **视频时长**：选择5秒、10秒或15秒

4. **开始注入**
   - 点击"🚀 开始注入"按钮
   - 等待处理完成（通常需要几秒钟）
   - 查看状态提示

5. **生成视频**
   - 注入成功后，即梦AI页面会自动填入图片和提示词
   - 点击即梦AI的生成按钮
   - 等待视频生成完成

## 📋 详细配置说明

### 图片URL要求

✅ **支持的格式**
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP
- SVG

✅ **URL要求**
- 必须是完整的HTTP或HTTPS链接
- 图片服务器必须允许跨域访问
- 建议图片大小在1-10MB之间

❌ **不支持的情况**
- 本地文件路径（如：C:\images\1.jpg）
- 需要登录才能访问的图片
- 防盗链保护的图片

### 提示词优化

**默认提示词：**
```
固定镜头，完美过渡效果，电影感调色，炫酷过渡
```

**提示词建议：**
- 描述镜头运动：固定镜头、推拉镜头、旋转镜头
- 指定视觉效果：电影感、科幻感、梦幻效果
- 说明过渡方式：淡入淡出、炫酷过渡、平滑过渡
- 添加风格描述：赛博朋克、古典风格、现代简约

**示例提示词：**
```
缓慢推进镜头，梦幻光效，柔和过渡，电影级调色
旋转镜头，科幻风格，炫酷特效，未来感十足
固定镜头，自然过渡，温暖色调，治愈系风格
```

### 时长选择建议

- **5秒**：适合简单的图片切换，处理速度快
- **10秒**：推荐选择，平衡效果和时间
- **15秒**：适合复杂场景，展示更多细节

## 🔧 故障排除

### 问题1：图片下载失败

**可能原因：**
- 图片URL不正确
- 图片服务器不允许跨域访问
- 网络连接问题

**解决方法：**
1. 在浏览器中直接访问图片URL，确认能正常显示
2. 尝试使用其他图片URL
3. 检查网络连接

### 问题2：注入失败

**可能原因：**
- 不在即梦AI页面
- 页面未完全加载
- 页面结构发生变化

**解决方法：**
1. 确认在正确的即梦AI页面
2. 刷新页面后重试
3. 等待页面完全加载后再使用扩展

### 问题3：提示词不生效

**可能原因：**
- 提示词输入框未找到
- 页面交互限制

**解决方法：**
1. 手动点击提示词输入框
2. 尝试手动输入一些文字后再使用扩展
3. 检查提示词是否符合平台要求

### 问题4：扩展无响应

**解决方法：**
1. 刷新即梦AI页面
2. 重新加载扩展：
   - 进入 `chrome://extensions/`
   - 找到扩展，点击刷新按钮
3. 重启浏览器

## 🛠️ 高级使用技巧

### 1. 批量处理

如果需要处理多个视频：
1. 准备多组图片URL
2. 在扩展面板中快速切换URL
3. 利用配置自动保存功能

### 2. 自定义图片

**推荐图片来源：**
- 免费图片网站：Unsplash、Pixabay
- AI生成图片：Midjourney、DALL-E
- 自己拍摄的照片（上传到图床）

**图片处理建议：**
- 保持首尾帧风格一致
- 确保图片清晰度足够
- 避免过于复杂的背景

### 3. 效果优化

**获得更好效果的技巧：**
1. 选择对比明显的首尾帧
2. 使用描述性强的提示词
3. 根据内容选择合适的时长
4. 多次尝试不同的参数组合

## 📞 技术支持

### 调试信息

如遇问题，可以查看调试信息：
1. 按F12打开开发者工具
2. 查看Console标签页的日志
3. 截图错误信息以便排查

### 常见错误代码

- `图片下载失败`：检查图片URL
- `未找到文件输入框`：页面可能未加载完成
- `注入失败`：尝试刷新页面

### 联系方式

如需帮助，请提供以下信息：
- Chrome浏览器版本
- 扩展版本
- 错误截图
- 使用的图片URL

---

**温馨提示：** 本扩展仅供学习研究使用，请遵守即梦AI平台的使用条款和相关法律法规。
