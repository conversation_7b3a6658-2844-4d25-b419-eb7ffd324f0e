<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        .config-section {
            margin-bottom: 15px;
        }
        .config-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .config-section input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .prompt-section {
            margin-bottom: 15px;
        }
        .prompt-section textarea {
            width: 100%;
            height: 60px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>🎬 即梦AI图片注入</h3>
    </div>
    
    <div class="config-section">
        <label for="firstFrameUrl">首帧图片URL:</label>
        <input type="url" id="firstFrameUrl" placeholder="http://www.leduoae.com/ceshitupian/1.png">
    </div>
    
    <div class="config-section">
        <label for="lastFrameUrl">尾帧图片URL:</label>
        <input type="url" id="lastFrameUrl" placeholder="http://www.leduoae.com/ceshitupian/2.png">
    </div>
    
    <div class="prompt-section">
        <label for="promptText">提示词:</label>
        <textarea id="promptText" placeholder="固定镜头，完美过渡效果，电影感调色，炫酷过渡"></textarea>
    </div>
    
    <div class="config-section">
        <label for="videoDuration">视频时长:</label>
        <select id="videoDuration">
            <option value="5s">5秒</option>
            <option value="10s" selected>10秒</option>
            <option value="15s">15秒</option>
        </select>
    </div>
    
    <div class="button-group">
        <button class="btn btn-primary" id="injectBtn">🚀 开始注入</button>
        <button class="btn btn-secondary" id="clearBtn">🧹 清空</button>
    </div>
    
    <div id="status" class="status" style="display: none;"></div>
    
    <script src="popup.js"></script>
</body>
</html>
