document.addEventListener('DOMContentLoaded', function() {
    const firstFrameUrlInput = document.getElementById('firstFrameUrl');
    const lastFrameUrlInput = document.getElementById('lastFrameUrl');
    const promptTextInput = document.getElementById('promptText');
    const videoDurationSelect = document.getElementById('videoDuration');
    const injectBtn = document.getElementById('injectBtn');
    const clearBtn = document.getElementById('clearBtn');
    const statusDiv = document.getElementById('status');

    // 加载保存的配置
    loadConfig();

    // 设置默认值
    if (!firstFrameUrlInput.value) {
        firstFrameUrlInput.value = 'http://www.leduoae.com/ceshitupian/1.png';
    }
    if (!lastFrameUrlInput.value) {
        lastFrameUrlInput.value = 'http://www.leduoae.com/ceshitupian/2.png';
    }
    if (!promptTextInput.value) {
        promptTextInput.value = '固定镜头，完美过渡效果，电影感调色，炫酷过渡';
    }

    // 保存配置
    function saveConfig() {
        const config = {
            firstFrameUrl: firstFrameUrlInput.value,
            lastFrameUrl: lastFrameUrlInput.value,
            promptText: promptTextInput.value,
            videoDuration: videoDurationSelect.value
        };
        chrome.storage.local.set({ config: config });
    }

    // 加载配置
    function loadConfig() {
        chrome.storage.local.get(['config'], function(result) {
            if (result.config) {
                const config = result.config;
                firstFrameUrlInput.value = config.firstFrameUrl || 'http://www.leduoae.com/ceshitupian/1.png';
                lastFrameUrlInput.value = config.lastFrameUrl || 'http://www.leduoae.com/ceshitupian/2.png';
                promptTextInput.value = config.promptText || '固定镜头，完美过渡效果，电影感调色，炫酷过渡';
                videoDurationSelect.value = config.videoDuration || '10s';
            }
        });
    }

    // 显示状态消息
    function showStatus(message, type = 'info') {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }

    // 验证URL格式
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    // 注入按钮点击事件
    injectBtn.addEventListener('click', async function() {
        const firstFrameUrl = firstFrameUrlInput.value.trim();
        const lastFrameUrl = lastFrameUrlInput.value.trim();
        const promptText = promptTextInput.value.trim();
        const videoDuration = videoDurationSelect.value;

        // 验证输入
        if (!firstFrameUrl || !lastFrameUrl) {
            showStatus('请填写首帧和尾帧图片URL', 'error');
            return;
        }

        if (!isValidUrl(firstFrameUrl) || !isValidUrl(lastFrameUrl)) {
            showStatus('请输入有效的图片URL', 'error');
            return;
        }

        if (!promptText) {
            showStatus('请输入提示词', 'error');
            return;
        }

        // 保存配置
        saveConfig();

        // 显示处理中状态
        showStatus('正在注入图片...', 'info');
        injectBtn.disabled = true;
        injectBtn.textContent = '处理中...';

        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('jimeng.jianying.com')) {
                showStatus('请在即梦AI页面使用此扩展', 'error');
                return;
            }

            // 向content script发送消息
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'injectImages',
                data: {
                    firstFrameUrl: firstFrameUrl,
                    lastFrameUrl: lastFrameUrl,
                    promptText: promptText,
                    videoDuration: videoDuration
                }
            });

            if (response && response.success) {
                showStatus('图片注入成功！', 'success');
            } else {
                showStatus(response?.error || '注入失败，请重试', 'error');
            }

        } catch (error) {
            console.error('注入失败:', error);
            showStatus('注入失败: ' + error.message, 'error');
        } finally {
            injectBtn.disabled = false;
            injectBtn.textContent = '🚀 开始注入';
        }
    });

    // 清空按钮点击事件
    clearBtn.addEventListener('click', async function() {
        showStatus('正在清空...', 'info');
        clearBtn.disabled = true;

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('jimeng.jianying.com')) {
                showStatus('请在即梦AI页面使用此扩展', 'error');
                return;
            }

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'clearInputs'
            });

            if (response && response.success) {
                showStatus('清空成功！', 'success');
            } else {
                showStatus('清空失败，请重试', 'error');
            }

        } catch (error) {
            console.error('清空失败:', error);
            showStatus('清空失败: ' + error.message, 'error');
        } finally {
            clearBtn.disabled = false;
        }
    });

    // 监听输入变化，自动保存配置
    [firstFrameUrlInput, lastFrameUrlInput, promptTextInput, videoDurationSelect].forEach(input => {
        input.addEventListener('change', saveConfig);
        input.addEventListener('input', saveConfig);
    });
});
