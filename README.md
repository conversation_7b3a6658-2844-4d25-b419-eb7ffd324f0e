# 即梦AI图片注入Chrome扩展

这是一个Chrome扩展，用于自动从网址读取首尾帧图片并注入到即梦AI页面，实现自动化的视频生成流程。

## 功能特点

- 🌐 **从网址读取图片**：直接从指定URL下载首尾帧图片，无需本地上传
- 🎬 **自动注入**：自动将图片注入到即梦AI页面的文件输入框
- 💭 **提示词注入**：自动填入自定义提示词
- ⏱️ **时长设置**：自动设置视频时长（5秒/10秒/15秒）
- 🧹 **温和清空**：温和地清空现有输入，不破坏页面结构
- 💾 **配置保存**：自动保存用户配置，下次使用更便捷

## 安装方法

### 方法1：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含扩展文件的文件夹
6. 扩展安装完成

### 方法2：打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择扩展文件夹，生成.crx文件
3. 将.crx文件拖拽到扩展管理页面进行安装

## 使用方法

### 1. 准备工作

确保您要使用的图片URL可以正常访问，默认配置：
- 首帧图片：`http://www.leduoae.com/ceshitupian/1.png`
- 尾帧图片：`http://www.leduoae.com/ceshitupian/2.png`

### 2. 打开即梦AI页面

访问：`https://jimeng.jianying.com/ai-tool/generate?type=video`

### 3. 使用扩展

1. 点击浏览器工具栏中的扩展图标
2. 在弹出的面板中：
   - 确认或修改首帧图片URL
   - 确认或修改尾帧图片URL
   - 输入或修改提示词
   - 选择视频时长
3. 点击"🚀 开始注入"按钮
4. 等待处理完成

### 4. 生成视频

注入完成后，即梦AI页面应该已经填入了图片和提示词，您可以：
1. 检查注入结果
2. 点击生成按钮开始制作视频
3. 等待视频生成完成

## 配置说明

### 图片URL要求

- 必须是有效的HTTP/HTTPS链接
- 必须是图片格式（jpg, jpeg, png, gif, bmp, webp, svg）
- 图片必须可以跨域访问
- 建议图片大小适中（1-10MB）

### 提示词建议

默认提示词：`固定镜头，完美过渡效果，电影感调色，炫酷过渡`

您可以根据需要自定义提示词，建议包含：
- 镜头运动描述
- 视觉效果要求
- 风格定义
- 过渡效果

### 视频时长选项

- 5秒：适合简短的过渡效果
- 10秒：默认选项，平衡效果和处理时间
- 15秒：更长的展示时间，适合复杂场景

## 故障排除

### 常见问题

1. **图片下载失败**
   - 检查图片URL是否正确
   - 确认图片服务器允许跨域访问
   - 尝试在浏览器中直接访问图片URL

2. **注入失败**
   - 确保在即梦AI页面使用扩展
   - 刷新页面后重试
   - 检查页面是否完全加载

3. **提示词不生效**
   - 检查提示词输入框是否可见
   - 尝试手动点击输入框后再使用扩展
   - 确认提示词内容符合平台要求

### 调试方法

1. 打开Chrome开发者工具（F12）
2. 查看Console标签页的日志信息
3. 检查Network标签页的网络请求
4. 在扩展管理页面查看扩展详情和错误信息

## 技术实现

### 核心技术

- **Manifest V3**：使用最新的Chrome扩展API
- **Content Scripts**：在页面中执行注入逻辑
- **Fetch API**：下载网络图片
- **File API**：将下载的图片转换为File对象
- **DOM操作**：模拟用户输入和文件选择

### 文件结构

```
chrome扩展注入/
├── manifest.json      # 扩展配置文件
├── popup.html         # 扩展弹窗界面
├── popup.js          # 弹窗逻辑脚本
├── content.js        # 内容脚本（页面注入）
├── background.js     # 后台脚本
└── README.md         # 说明文档
```

## 更新日志

### v1.0 (2024-08-02)
- 初始版本发布
- 实现基本的图片URL注入功能
- 支持提示词和时长设置
- 添加配置保存功能

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## 支持

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件到开发者邮箱

---

**注意**：本扩展仅在即梦AI官方页面使用，请确保遵守平台的使用规则和条款。
