// 内容脚本 - 在即梦AI页面中运行
console.log('即梦AI图片注入扩展已加载');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);

    if (request.action === 'injectImages') {
        handleImageInjection(request.data)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放
    }

    if (request.action === 'clearInputs') {
        handleClearInputs()
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true;
    }
});

// 处理图片注入
async function handleImageInjection(data) {
    try {
        console.log('开始图片注入流程:', data);

        // 步骤1: 温和清空现有输入
        console.log('步骤1: 温和清空现有输入');
        await gentleClearInputs();

        // 步骤2: 从URL下载图片并转换为File对象
        console.log('步骤2: 下载图片');
        const firstFrameFile = await downloadImageAsFile(data.firstFrameUrl, 'first_frame.png');
        const lastFrameFile = await downloadImageAsFile(data.lastFrameUrl, 'last_frame.png');

        if (!firstFrameFile || !lastFrameFile) {
            throw new Error('图片下载失败');
        }

        // 步骤3: 注入辅助函数
        console.log('步骤3: 注入辅助函数');
        await injectHelperFunctions();

        // 步骤4: 注入首帧
        console.log('步骤4: 注入首帧');
        const firstFrameSuccess = await injectImageToInput(firstFrameFile, 0);
        if (!firstFrameSuccess) {
            throw new Error('首帧注入失败');
        }

        await sleep(1000);

        // 步骤5: 注入尾帧
        console.log('步骤5: 注入尾帧');
        const lastFrameSuccess = await injectImageToInput(lastFrameFile, 1);
        if (!lastFrameSuccess) {
            console.log('尾帧注入失败，但首帧已成功');
        }

        await sleep(2000);

        // 步骤6: 注入提示词
        console.log('步骤6: 注入提示词');
        const promptSuccess = await injectPrompt(data.promptText);
        if (!promptSuccess) {
            console.log('提示词注入失败');
        }

        await sleep(1000);

        // 步骤7: 设置视频时长
        console.log('步骤7: 设置视频时长');
        const durationSuccess = await setVideoDuration(data.videoDuration);
        if (!durationSuccess) {
            console.log('时长设置失败');
        }

        return { success: true, message: '图片注入完成' };

    } catch (error) {
        console.error('图片注入失败:', error);
        return { success: false, error: error.message };
    }
}

// 处理清空输入
async function handleClearInputs() {
    try {
        await gentleClearInputs();
        return { success: true, message: '清空完成' };
    } catch (error) {
        console.error('清空失败:', error);
        return { success: false, error: error.message };
    }
}

// 温和清空输入
async function gentleClearInputs() {
    return new Promise((resolve) => {
        const result = {
            resetInputs: 0,
            preservedInputs: 0
        };

        // 重置文件输入
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach((input, index) => {
            try {
                input.value = '';
                const emptyTransfer = new DataTransfer();
                input.files = emptyTransfer.files;
                result.resetInputs++;
                console.log(`重置文件输入 ${index}`);
            } catch (e) {
                console.log(`重置文件输入 ${index} 失败:`, e);
            }
        });

        result.preservedInputs = fileInputs.length;

        // 清空提示词输入
        const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
        if (textarea) {
            textarea.value = '';
            textarea.dispatchEvent(new Event('input', { bubbles: true }));
        }

        const input = document.querySelector('input.prompt-input-ajcKzc');
        if (input) {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
        }

        console.log('温和清空结果:', result);
        resolve(result);
    });
}

// 从URL下载图片并转换为File对象
async function downloadImageAsFile(url, filename) {
    try {
        console.log('下载图片:', url);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        
        // 检查是否为图片
        if (!blob.type.startsWith('image/')) {
            throw new Error('下载的文件不是图片格式');
        }

        // 转换为File对象
        const file = new File([blob], filename, { type: blob.type });
        console.log('图片下载成功:', filename, file.size, 'bytes');
        
        return file;
    } catch (error) {
        console.error('下载图片失败:', url, error);
        return null;
    }
}

// 注入辅助函数
async function injectHelperFunctions() {
    return new Promise((resolve) => {
        // 设置文件到输入框的辅助函数
        window.setFileToInput = function(input, file) {
            try {
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                input.files = dataTransfer.files;
                
                // 触发事件
                const changeEvent = new Event('change', { bubbles: true });
                input.dispatchEvent(changeEvent);
                
                const inputEvent = new Event('input', { bubbles: true });
                input.dispatchEvent(inputEvent);
                
                return true;
            } catch (error) {
                console.error('设置文件失败:', error);
                return false;
            }
        };

        console.log('辅助函数注入完成');
        resolve();
    });
}

// 注入图片到指定输入框
async function injectImageToInput(file, inputIndex = 0) {
    return new Promise((resolve) => {
        try {
            const inputs = document.querySelectorAll('input[type="file"]');
            if (inputs.length === 0) {
                console.error('未找到文件输入框');
                resolve(false);
                return;
            }

            let targetInput = inputs[inputIndex];
            if (!targetInput && inputIndex > 0) {
                // 如果指定索引不存在，尝试使用任何可用的输入框
                for (let i = 0; i < inputs.length; i++) {
                    if (inputs[i]) {
                        targetInput = inputs[i];
                        break;
                    }
                }
            }

            if (!targetInput) {
                console.error('未找到目标输入框');
                resolve(false);
                return;
            }

            // 强制显示输入框
            targetInput.style.display = 'block !important';
            targetInput.style.visibility = 'visible !important';
            targetInput.style.opacity = '1 !important';

            // 设置文件
            const success = window.setFileToInput(targetInput, file);
            console.log(`图片注入到输入框 ${inputIndex}:`, success ? '成功' : '失败');
            
            resolve(success);
        } catch (error) {
            console.error('图片注入失败:', error);
            resolve(false);
        }
    });
}

// 注入提示词
async function injectPrompt(promptText) {
    return new Promise(async (resolve) => {
        try {
            console.log('开始注入提示词:', promptText);

            // 查找textarea
            const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
            if (textarea) {
                // 聚焦并清空
                textarea.focus();
                await sleep(200);
                
                textarea.select();
                document.execCommand('selectAll');
                document.execCommand('delete');
                
                // 设置值
                textarea.value = promptText;
                
                // 触发事件
                const events = ['input', 'change', 'keyup', 'blur'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    textarea.dispatchEvent(event);
                });

                console.log('提示词注入成功 (textarea)');
                resolve(true);
                return;
            }

            // 查找input
            const input = document.querySelector('input.prompt-input-ajcKzc');
            if (input) {
                input.focus();
                await sleep(200);
                
                input.value = promptText;
                
                const events = ['input', 'change'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    input.dispatchEvent(event);
                });

                console.log('提示词注入成功 (input)');
                resolve(true);
                return;
            }

            console.log('未找到提示词输入框');
            resolve(false);

        } catch (error) {
            console.error('提示词注入失败:', error);
            resolve(false);
        }
    });
}

// 设置视频时长
async function setVideoDuration(duration) {
    return new Promise(async (resolve) => {
        try {
            console.log('设置视频时长:', duration);

            // 查找时长选择器
            const selectElements = document.querySelectorAll('.lv-select, [role="combobox"]');
            let durationSelect = null;

            for (const select of selectElements) {
                const valueSpan = select.querySelector('.lv-select-view-value');
                if (valueSpan) {
                    const currentValue = valueSpan.textContent.trim();
                    if (currentValue.includes('s') || currentValue.includes('秒')) {
                        durationSelect = select;
                        console.log('找到时长选择器，当前值:', currentValue);
                        break;
                    }
                }
            }

            if (!durationSelect) {
                console.log('未找到时长选择器');
                resolve(false);
                return;
            }

            // 点击选择器打开下拉菜单
            durationSelect.click();
            await sleep(500);

            // 查找目标选项
            const options = document.querySelectorAll('.lv-select-option');
            let targetOption = null;

            for (const option of options) {
                const optionText = option.textContent.trim();
                if (optionText === duration) {
                    targetOption = option;
                    break;
                }
            }

            if (targetOption) {
                targetOption.click();
                console.log('时长设置成功:', duration);
                resolve(true);
            } else {
                console.log('未找到目标时长选项:', duration);
                resolve(false);
            }

        } catch (error) {
            console.error('时长设置失败:', error);
            resolve(false);
        }
    });
}

// 辅助函数：延时
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
