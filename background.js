// 后台脚本
console.log('即梦AI图片注入扩展后台脚本已启动');

// 监听扩展安装事件
chrome.runtime.onInstalled.addListener((details) => {
    console.log('扩展已安装/更新:', details.reason);
    
    if (details.reason === 'install') {
        // 首次安装时的初始化
        console.log('首次安装扩展');
        
        // 设置默认配置
        const defaultConfig = {
            firstFrameUrl: 'http://www.leduoae.com/ceshitupian/1.png',
            lastFrameUrl: 'http://www.leduoae.com/ceshitupian/2.png',
            promptText: '固定镜头，完美过渡效果，电影感调色，炫酷过渡',
            videoDuration: '10s'
        };
        
        chrome.storage.local.set({ config: defaultConfig }, () => {
            console.log('默认配置已保存');
        });
    }
});

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当即梦AI页面加载完成时，可以执行一些初始化操作
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('jimeng.jianying.com')) {
        console.log('即梦AI页面加载完成:', tab.url);
    }
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('后台脚本收到消息:', request);
    
    if (request.action === 'log') {
        console.log('来自content script的日志:', request.message);
    }
    
    // 可以在这里处理需要后台脚本权限的操作
    return true;
});

// 处理扩展图标点击事件（如果需要）
chrome.action.onClicked.addListener((tab) => {
    console.log('扩展图标被点击，当前标签页:', tab.url);
    
    // 检查是否在即梦AI页面
    if (tab.url && tab.url.includes('jimeng.jianying.com')) {
        // 在即梦AI页面，popup会自动打开
        console.log('在即梦AI页面，popup将自动打开');
    } else {
        // 不在即梦AI页面，可以提示用户
        console.log('不在即梦AI页面');
    }
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    console.log('存储发生变化:', changes, namespace);
    
    if (changes.config) {
        console.log('配置已更新:', changes.config.newValue);
    }
});

// 错误处理
chrome.runtime.onSuspend.addListener(() => {
    console.log('后台脚本即将被挂起');
});

// 提供一些实用的辅助函数
const utils = {
    // 检查URL是否有效
    isValidUrl: (string) => {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    },
    
    // 获取文件扩展名
    getFileExtension: (url) => {
        try {
            const pathname = new URL(url).pathname;
            return pathname.split('.').pop().toLowerCase();
        } catch (_) {
            return '';
        }
    },
    
    // 检查是否为图片URL
    isImageUrl: (url) => {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const extension = utils.getFileExtension(url);
        return imageExtensions.includes(extension);
    }
};

// 将工具函数暴露给其他脚本使用
globalThis.extensionUtils = utils;
