<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4285f4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34a853;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#bg)" stroke="#fff" stroke-width="4"/>
  
  <!-- 图片框架1 -->
  <rect x="20" y="30" width="35" height="25" fill="#fff" stroke="#333" stroke-width="2" rx="3"/>
  <rect x="23" y="33" width="29" height="19" fill="#e8f0fe"/>
  <circle cx="28" cy="38" r="2" fill="#4285f4"/>
  <polygon points="25,48 30,43 35,48 40,45 47,52" fill="#34a853"/>
  
  <!-- 图片框架2 -->
  <rect x="73" y="30" width="35" height="25" fill="#fff" stroke="#333" stroke-width="2" rx="3"/>
  <rect x="76" y="33" width="29" height="19" fill="#fef7e0"/>
  <circle cx="81" cy="38" r="2" fill="#fbbc04"/>
  <polygon points="78,48 83,43 88,48 93,45 100,52" fill="#ea4335"/>
  
  <!-- 箭头 -->
  <path d="M 58 42 L 68 42 L 65 38 M 68 42 L 65 46" stroke="#fff" stroke-width="3" fill="none" stroke-linecap="round"/>
  
  <!-- 视频播放图标 -->
  <circle cx="64" cy="85" r="20" fill="#fff" opacity="0.9"/>
  <polygon points="58,75 58,95 78,85" fill="#4285f4"/>
  
  <!-- 装饰性元素 -->
  <circle cx="25" cy="85" r="3" fill="#fff" opacity="0.7"/>
  <circle cx="103" cy="85" r="3" fill="#fff" opacity="0.7"/>
  <circle cx="35" cy="95" r="2" fill="#fff" opacity="0.5"/>
  <circle cx="93" cy="95" r="2" fill="#fff" opacity="0.5"/>
</svg>
